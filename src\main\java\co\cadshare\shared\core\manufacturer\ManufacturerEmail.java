package co.cadshare.shared.core.manufacturer;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class ManufacturerEmail {
    
    private Integer id;
    private int manufacturerId;
    private String label;
    private String email;
    private Timestamp createdDate;
    private Integer createdByUserId;
    private Timestamp modifiedDate;
    private Integer modifiedByUserId;
    
    public ManufacturerEmail() {}
    
    public ManufacturerEmail(String label, String email) {
        this.label = label;
        this.email = email;
    }
}
